{"hash": "5bd010b0", "configHash": "8894da8b", "lockfileHash": "d9a4c3dd", "browserHash": "cc66efd8", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "a60a6d0a", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "f2e8e36b", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "477aae18", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "99a869c5", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "f77f651b", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "df96880a", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "58b51ca7", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "0a8671e7", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "446187f0", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "14ddecc5", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "065ca630", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "efcd41b1", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "aec426dd", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../../../node_modules/@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "a5fd9a13", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "e4b01815", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "e95cb6d6", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "61e9689b", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c64fcf71", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "10f437eb", "needsInterop": false}, "next-themes": {"src": "../../../../node_modules/next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "d46003f0", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ab13719f", "needsInterop": true}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "d50bdc4d", "needsInterop": false}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "3b87cbd8", "needsInterop": true}, "recharts": {"src": "../../../../node_modules/recharts/es6/index.js", "file": "recharts.js", "fileHash": "a7c82173", "needsInterop": false}, "sonner": {"src": "../../../../node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "e6a3a4b2", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4766afac", "needsInterop": false}}, "chunks": {"chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-Z6N7IM46": {"file": "chunk-Z6N7IM46.js"}, "chunk-RWF7UEWW": {"file": "chunk-RWF7UEWW.js"}, "chunk-BBEI5V4X": {"file": "chunk-BBEI5V4X.js"}, "chunk-EMSPKVCK": {"file": "chunk-EMSPKVCK.js"}, "chunk-R7ZVENDU": {"file": "chunk-R7ZVENDU.js"}, "chunk-WFXZHWCA": {"file": "chunk-WFXZHWCA.js"}, "chunk-6RHS4UUI": {"file": "chunk-6RHS4UUI.js"}, "chunk-HJRPB4TZ": {"file": "chunk-HJRPB4TZ.js"}, "chunk-IBI677K4": {"file": "chunk-IBI677K4.js"}, "chunk-SXX54OC7": {"file": "chunk-SXX54OC7.js"}, "chunk-GXD7YJGF": {"file": "chunk-GXD7YJGF.js"}, "chunk-K6YFPJWF": {"file": "chunk-K6YFPJWF.js"}, "chunk-NB24HQGR": {"file": "chunk-NB24HQGR.js"}, "chunk-6AOXWRVX": {"file": "chunk-6AOXWRVX.js"}, "chunk-DLLDB44G": {"file": "chunk-DLLDB44G.js"}, "chunk-7QGA4JVC": {"file": "chunk-7QGA4JVC.js"}, "chunk-AVJPV5ZH": {"file": "chunk-AVJPV5ZH.js"}, "chunk-JYSI5OBP": {"file": "chunk-JYSI5OBP.js"}, "chunk-7URR3GLA": {"file": "chunk-7URR3GLA.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}