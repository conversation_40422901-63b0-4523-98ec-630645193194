{"name": "smartswap-backend", "version": "1.0.0", "description": "SmartSwap Scheduling System Backend API", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "dev:full": "concurrently \"npm run dev\" \"cd .. && npm run dev\"", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist", "seed:real-schedules": "npx ts-node src/scripts/seedRealSchedules.ts", "seed:users": "npx ts-node src/scripts/seedUsersFromSchedules.ts", "create:test-user": "npx ts-node src/scripts/createTestUser.ts", "create:demo-users": "npx ts-node src/scripts/createDemoUsers.ts"}, "keywords": ["scheduling", "shift-management", "api", "express", "mongodb"], "author": "SmartSwap Team", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/joi": "^17.2.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.23", "concurrently": "^9.1.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}