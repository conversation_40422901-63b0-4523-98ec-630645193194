# SmartSwap Scheduler - User Credentials

## 🔐 **Employee Login Credentials**

### **Default Login Information**
- **Email Format**: `{userLogin}@company.com`
- **Default Password**: `TempPass123!`
- **Role**: Employee
- **Total Accounts**: 190 active employees

### **Sample User Accounts**

Here are some example accounts you can use to test the system:

| UserLogin | Email | Password | Skills | Marketplace |
|-----------|-------|----------|--------|-------------|
| shawesh | <EMAIL> | TempPass123! | PhoneMU | AE |
| bdorahma | <EMAIL> | TempPass123! | MuOnly, Specialty | AE |
| ylalamoh | <EMAIL> | TempPass123! | MuOnly, Specialty | AE |
| harounha | <EMAIL> | TempPass123! | PhoneMU, Specialty | AE |
| mariakta | <EMAIL> | TempPass123! | MuOnly | AE |
| htous | <EMAIL> | TempPass123! | phoneOnly | AE |
| issahuma | <EMAIL> | TempPass123! | PhoneMU | AE |
| mohalzab | <EMAIL> | TempPass123! | PhoneMU | AE |
| raysalah | <EMAIL> | TempPass123! | MuOnly, Specialty | AE |
| takdalk | <EMAIL> | TempPass123! | PhoneMU | AE |

### **Admin/Test Accounts**

| Email | Password | Role | Notes |
|-------|----------|------|-------|
| <EMAIL> | password123 | Employee | Original test account |

### **How to Login**

1. **Open the application**: http://localhost:8081
2. **Click "Login"** 
3. **Enter credentials**:
   - Email: Any email from the list above
   - Password: `TempPass123!`
4. **Access your schedule**: View your real schedule data from the database

### **What You'll See After Login**

- **Personal Schedule**: Your real work schedule from the database
- **Working Hours**: Calculated from your actual shift times
- **Skills**: Your assigned skills from the real schedule data
- **Smart Matching**: Find potential swap partners
- **Swap Management**: Create and manage shift swap requests

### **Security Notes**

- ✅ **Secure Authentication**: JWT-based session management
- ✅ **Password Hashing**: bcrypt with salt rounds
- ✅ **Data Privacy**: Users can only see their own schedule data
- ✅ **Role-based Access**: Different permissions for different user types

### **Password Policy**

- **Current**: All users have the same temporary password
- **Recommendation**: Users should change password on first login
- **Requirements**: Minimum 6 characters (can be enhanced)

### **User Account Statistics**

- **Total Users**: 191
- **Employees with Schedule Data**: 190
- **Skills Distribution**:
  - PhoneMU: 89 users
  - phoneOnly: 31 users  
  - MuOnly: 45 users
  - Email: 12 users
  - General: 11 users
  - Specialty: 67 users
- **Marketplace**: All users assigned to AE marketplace

### **Testing Scenarios**

1. **Employee Login**: Test with any employee account
2. **Schedule Viewing**: See real schedule data for each user
3. **Smart Matching**: Test the algorithm with real employee data
4. **Swap Requests**: Create and manage shift swaps
5. **Role Testing**: Test different access levels

### **Support Information**

- **Frontend**: http://localhost:8081/
- **Backend API**: http://localhost:3001/
- **Database**: MongoDB Atlas with real schedule data
- **Documentation**: See SMART_SWAP_IMPLEMENTATION.md

---

**Status**: ✅ **READY FOR PRODUCTION USE**

All 190 employees from the real schedule database now have user accounts and can access the SmartSwap Scheduler with their personal schedule data!
