
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { StatusBadge } from '@/components/ui/status-badge';
import { MatchScoreBar } from '@/components/ui/match-score-bar';
import { SmartMatchSkeleton, SmartMatchHeaderSkeleton } from '@/components/ui/smart-match-skeleton';
import { LiveTrackingIndicator } from '@/components/ui/live-tracking-indicator';
import { MatchCelebration } from '@/components/ui/celebration-animation';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useSwapIntents, useSmartMatches } from '@/hooks/useSwapIntents';
import { useDashboardStats } from '@/hooks/useDashboard';
import { CreateSwapIntentModal } from '@/components/CreateSwapIntentModal';
import { Zap, Clock, MapPin, Star, Users, ArrowRight, Plus } from 'lucide-react';

interface SmartMatchViewProps {
  userRole: string;
}

export const SmartMatchView: React.FC<SmartMatchViewProps> = ({ userRole }) => {
  const [selectedMatch, setSelectedMatch] = useState<string | null>(null);
  const [searchStatus, setSearchStatus] = useState<"searching" | "matching" | "found" | "no-matches" | "idle">("idle");
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [showCelebration, setShowCelebration] = useState(false);
  const [celebrationMessage, setCelebrationMessage] = useState("");
  const [activeIntentId, setActiveIntentId] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Hooks
  const { activeIntents, isLoadingActive } = useSwapIntents();
  const { matches, isLoading: isLoadingMatches, findMatches, isSearching } = useSmartMatches(activeIntentId);
  const { dashboardStats, isLoading: isLoadingDashboard } = useDashboardStats();
  const analytics = useAnalytics();
  const trackMatchAccepted = analytics?.trackMatchAccepted || ((data: any) => console.log('Match accepted:', data));
  const trackSearchPerformed = analytics?.trackSearchPerformed || ((data: any) => console.log('Search performed:', data));

  // Set the first active intent as default when intents load
  useEffect(() => {
    if (activeIntents.length > 0 && !activeIntentId) {
      setActiveIntentId(activeIntents[0]._id);
    }
  }, [activeIntents, activeIntentId]);

  // Update search status based on matches
  useEffect(() => {
    if (isSearching) {
      // Don't change status while searching
      return;
    }

    if (matches.length > 0) {
      setSearchStatus("found");
    } else if (activeIntentId && !isLoadingMatches) {
      // Check if we just completed a search (status was searching/matching)
      if (searchStatus === "searching" || searchStatus === "matching") {
        setSearchStatus("no-matches");
      } else if (searchStatus === "idle") {
        // Keep idle status if we haven't searched yet
        setSearchStatus("idle");
      }
    }
  }, [matches, activeIntentId, isLoadingMatches, isSearching, searchStatus]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 80) return 'text-blue-600 bg-blue-100';
    return 'text-yellow-600 bg-yellow-100';
  };

  const getCompatibilityColor = (compatibility: string) => {
    if (compatibility === 'Perfect Match') return 'bg-green-500';
    if (compatibility === 'High Match') return 'bg-blue-500';
    return 'bg-yellow-500';
  };

  // Timer for search elapsed time
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isSearching) {
      interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
    } else {
      setTimeElapsed(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isSearching]);

  // Handle match connection with celebration
  const handleConnect = async (matchId: string, targetIntentId: string) => {
    try {
      const match = matches.find(m => m.id === matchId);
      if (match) {
        trackMatchAccepted(match);

        // Here you would implement the actual connection logic
        // For now, we'll show a success message
        setCelebrationMessage(`Successfully connected with match!`);
        setShowCelebration(true);
      }
    } catch (error) {
      console.error('Error connecting match:', error);
    }
  };

  // Perform new search
  const handleNewSearch = async () => {
    if (!activeIntentId) {
      return;
    }

    setSearchStatus("searching");
    setTimeElapsed(0);

    // Track search event
    trackSearchPerformed({
      userRole,
      resultsCount: matches.length,
      timestamp: new Date().toISOString(),
    });

    // Simulate search phases for UX
    setTimeout(() => setSearchStatus("matching"), 1000);

    // Perform actual search
    findMatches(activeIntentId);

    // Wait for search to complete, then update status based on results
    setTimeout(() => {
      // The useEffect will handle setting the correct status based on matches.length
      // This timeout just ensures the search animation completes
    }, 2000);
  };

  // Show loading state
  if (isLoadingActive) {
    return (
      <div className="p-6 max-w-7xl mx-auto space-y-6">
        <SmartMatchHeaderSkeleton />
        <SmartMatchSkeleton count={3} />
      </div>
    );
  }

  // Show no intents state
  if (activeIntents.length === 0) {
    return (
      <div className="p-6 max-w-7xl mx-auto space-y-6">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2 flex items-center space-x-3">
            <Zap className="h-8 w-8 text-blue-600" />
            <span>SmartSwap Matchmaking</span>
          </h2>
          <p className="text-gray-600">
            AI-powered shift matching finds the perfect swap partners based on skills, preferences, and availability.
          </p>
        </div>

        <Card className="text-center py-12">
          <CardContent>
            <div className="flex flex-col items-center space-y-4">
              <Plus className="h-16 w-16 text-gray-400" />
              <h3 className="text-xl font-semibold text-gray-900">No Active Swap Intents</h3>
              <p className="text-gray-600 max-w-md">
                Create a swap intent to start finding intelligent matches for your shifts.
              </p>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => setShowCreateModal(true)}
              >
                Create Swap Intent
              </Button>
            </div>
          </CardContent>
        </Card>

        <CreateSwapIntentModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
        />
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Celebration Animation */}
      <MatchCelebration
        isVisible={showCelebration}
        onComplete={() => setShowCelebration(false)}
        message={celebrationMessage}
      />

      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2 flex items-center space-x-3">
          <Zap className="h-8 w-8 text-blue-600" />
          <span>SmartSwap Matchmaking</span>
        </h2>
        <p className="text-gray-600">
          AI-powered shift matching finds the perfect swap partners based on skills, preferences, and availability.
        </p>

        {/* Intent Selection and Search */}
        <div className="mt-4 flex items-center space-x-4">
          {activeIntents.length > 1 && (
            <select
              value={activeIntentId || ''}
              onChange={(e) => setActiveIntentId(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {activeIntents.map((intent) => (
                <option key={intent._id} value={intent._id}>
                  Intent #{intent._id.slice(-6)} - Priority {intent.priority}
                </option>
              ))}
            </select>
          )}
          <Button
            onClick={handleNewSearch}
            disabled={isSearching || !activeIntentId}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSearching ? "Searching..." : "Find New Matches"}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowCreateModal(true)}
            className="border-blue-600 text-blue-600 hover:bg-blue-50"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Intent
          </Button>
        </div>
      </div>

      {/* Live Tracking Indicator */}
      {(isSearching || searchStatus !== "idle") && (
        <LiveTrackingIndicator
          isActive={isSearching}
          status={searchStatus}
          matchCount={matches.length}
          timeElapsed={timeElapsed}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardHeader>
            <CardTitle className="text-white">Active Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {isLoadingDashboard ? '...' : dashboardStats?.activeRequests || 0}
            </div>
            <p className="text-blue-100">
              {isLoadingDashboard ? 'Loading...' :
                dashboardStats?.trends.activeRequestsChange !== undefined ?
                  `${dashboardStats.trends.activeRequestsChange >= 0 ? '+' : ''}${dashboardStats.trends.activeRequestsChange} from yesterday` :
                  'No change data'
              }
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardHeader>
            <CardTitle className="text-white">Successful Matches</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {isLoadingDashboard ? '...' : dashboardStats?.successfulMatches || 0}
            </div>
            <p className="text-green-100">
              {isLoadingDashboard ? 'Loading...' :
                dashboardStats?.trends.successfulMatchesChange !== undefined ?
                  `${dashboardStats.trends.successfulMatchesChange >= 0 ? '+' : ''}${dashboardStats.trends.successfulMatchesChange} this week` :
                  'No change data'
              }
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardHeader>
            <CardTitle className="text-white">AI Confidence</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {isLoadingDashboard ? '...' : `${dashboardStats?.aiConfidence || 0}%`}
            </div>
            <p className="text-purple-100">
              {isLoadingDashboard ? 'Loading...' :
                dashboardStats?.trends.aiConfidenceChange !== undefined ?
                  `${dashboardStats.trends.aiConfidenceChange >= 0 ? '+' : ''}${dashboardStats.trends.aiConfidenceChange}% trend` :
                  'Stable performance'
              }
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <span>Top SmartSwap Recommendations</span>
          </CardTitle>
          <CardDescription>
            Ranked by compatibility score and mutual benefit
          </CardDescription>
        </CardHeader>
        <CardContent>
          {matches.length === 0 ? (
            <div className="text-center py-8">
              <div className="flex flex-col items-center space-y-4">
                <Star className="h-12 w-12 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900">No Matches Found</h3>
                <p className="text-gray-600">
                  {searchStatus === "idle"
                    ? "Click 'Find New Matches' to search for compatible swap partners."
                    : "No compatible matches found for your current intent. Try adjusting your preferences."
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {matches.map((match) => (
                <div
                  key={match.id}
                  className={`p-6 border rounded-lg transition-all duration-200 cursor-pointer ${
                    selectedMatch === match.id
                      ? 'border-blue-500 bg-blue-50 shadow-md'
                      : 'border-gray-200 hover:border-blue-300 hover:shadow-sm'
                  }`}
                  onClick={() => setSelectedMatch(selectedMatch === match.id ? null : match.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-gray-500" />
                          <span className="font-semibold text-gray-900">Match #{match.id.slice(-6)}</span>
                        </div>
                        <StatusBadge
                          variant={
                            match.matchScore >= 90 ? "perfect-match" :
                            match.matchScore >= 80 ? "high-match" : "good-match"
                          }
                        >
                          {match.matchScore}% Match
                        </StatusBadge>
                        <StatusBadge
                          variant={
                            match.compatibility === 'Perfect Match' ? "perfect-match" :
                            match.compatibility === 'High Match' ? "high-match" : "good-match"
                          }
                        >
                          {match.compatibility}
                        </StatusBadge>
                      </div>

                      <div className="mb-4">
                        <p className="text-sm text-gray-600 mb-2">Match Reason:</p>
                        <p className="text-sm text-gray-900">{match.reason}</p>
                      </div>

                      <div className="flex items-center space-x-2 mb-3">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">Calculated:</span>
                        <span className="text-sm text-gray-900">
                          {new Date(match.calculatedAt).toLocaleString()}
                        </span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleConnect(match.id, match.targetIntentId);
                        }}
                      >
                        Connect
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-blue-600 text-blue-600 hover:bg-blue-50"
                        onClick={(e) => e.stopPropagation()}
                      >
                        Details
                      </Button>
                    </div>
                  </div>

                  {selectedMatch === match.id && (
                    <div className="mt-4 pt-4 border-t border-blue-200 bg-white p-4 rounded space-y-4">
                      <h4 className="font-medium text-gray-900 mb-4">Match Analysis</h4>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <MatchScoreBar
                          score={match.matchScore}
                          label="Overall Compatibility"
                          animated={true}
                          reasons={match.factors}
                        />

                        <MatchScoreBar
                          score={Math.min(match.matchScore + 5, 100)}
                          label="Match Confidence"
                          animated={true}
                        />
                      </div>

                      <div className="mt-4 space-y-3">
                        <h5 className="text-sm font-medium text-gray-700">Matching Factors:</h5>
                        {match.factors.map((factor, index) => (
                          <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div className={`w-3 h-3 rounded-full mt-1 ${
                              factor.status === 'positive' ? 'bg-green-500' :
                              factor.status === 'negative' ? 'bg-red-500' : 'bg-yellow-500'
                            }`} />
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-900">{factor.factor}</span>
                                <span className="text-xs text-gray-500">
                                  Weight: {Math.round(factor.weight * 100)}%
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">{factor.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                        <h5 className="text-sm font-medium text-blue-900 mb-2">Match Summary:</h5>
                        <p className="text-sm text-blue-800">{match.reason}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Swap Intent Modal */}
      <CreateSwapIntentModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  );
};
