{"code":"ENOTFOUND","hostname":"_mongodb._tcp.3","level":"error","message":"Error connecting to MongoDB: querySrv ENOTFOUND _mongodb._tcp.3","service":"smartswap-backend","stack":"Error: querySrv ENOTFOUND _mongodb._tcp.3\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/promises:294:17)","syscall":"querySrv","timestamp":"2025-05-28T08:20:48.264Z"}
{"code":"ENOTFOUND","hostname":"_mongodb._tcp.3","level":"error","message":"Error connecting to MongoDB: querySrv ENOTFOUND _mongodb._tcp.3","service":"smartswap-backend","stack":"Error: querySrv ENOTFOUND _mongodb._tcp.3\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/promises:294:17)","syscall":"querySrv","timestamp":"2025-05-28T08:21:00.609Z"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-i672liu-shard-00-00.lsp4r4u.mongodb.net:27017":{"$clusterTime":null,"address":"ac-i672liu-shard-00-00.lsp4r4u.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":40476110,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-i672liu-shard-00-01.lsp4r4u.mongodb.net:27017":{"$clusterTime":null,"address":"ac-i672liu-shard-00-01.lsp4r4u.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":40476285,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-i672liu-shard-00-02.lsp4r4u.mongodb.net:27017":{"$clusterTime":null,"address":"ac-i672liu-shard-00-02.lsp4r4u.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":40476093,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-6xk9fo-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"errorLabelSet":{},"level":"error","message":"Error connecting to MongoDB: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-i672liu-shard-00-00.lsp4r4u.mongodb.net:27017":{"$clusterTime":null,"address":"ac-i672liu-shard-00-00.lsp4r4u.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":40476110,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-i672liu-shard-00-01.lsp4r4u.mongodb.net:27017":{"$clusterTime":null,"address":"ac-i672liu-shard-00-01.lsp4r4u.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":40476285,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-i672liu-shard-00-02.lsp4r4u.mongodb.net:27017":{"$clusterTime":null,"address":"ac-i672liu-shard-00-02.lsp4r4u.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":40476093,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-6xk9fo-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"smartswap-backend","stack":"MongooseServerSelectionError: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/\n    at _handleConnectionErrors (D:\\Scheduler-Lovable\\backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (D:\\Scheduler-Lovable\\backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDB (D:\\Scheduler-Lovable\\backend\\src\\config\\database.ts:8:18)\n    at async startServer (D:\\Scheduler-Lovable\\backend\\src\\index.ts:10:7)","timestamp":"2025-05-28T08:21:57.875Z"}
{"code":8000,"codeName":"AtlasError","connectionGeneration":0,"errorLabelSet":{},"errorResponse":{"code":8000,"codeName":"AtlasError","errmsg":"bad auth : authentication failed","ok":0},"level":"error","message":"Error connecting to MongoDB: bad auth : authentication failed","ok":0,"service":"smartswap-backend","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connection.ts:551:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connection.ts:625:22)\n    at async continueScramConversation (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:187:13)\n    at async executeScram (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:114:3)\n    at async ScramSHA1.auth (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:60:12)\n    at async performInitialHandshake (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connect.ts:164:7)\n    at async connect (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connect.ts:43:5)","timestamp":"2025-05-28T08:22:39.919Z"}
{"code":8000,"codeName":"AtlasError","connectionGeneration":0,"errorLabelSet":{},"errorResponse":{"code":8000,"codeName":"AtlasError","errmsg":"bad auth : authentication failed","ok":0},"level":"error","message":"Error connecting to MongoDB: bad auth : authentication failed","ok":0,"service":"smartswap-backend","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connection.ts:551:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connection.ts:625:22)\n    at async continueScramConversation (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:187:13)\n    at async executeScram (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:114:3)\n    at async ScramSHA1.auth (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:60:12)\n    at async performInitialHandshake (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connect.ts:164:7)\n    at async connect (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connect.ts:43:5)","timestamp":"2025-05-28T08:22:52.287Z"}
{"code":8000,"codeName":"AtlasError","connectionGeneration":0,"errorLabelSet":{},"errorResponse":{"code":8000,"codeName":"AtlasError","errmsg":"bad auth : authentication failed","ok":0},"level":"error","message":"Error connecting to MongoDB: bad auth : authentication failed","ok":0,"service":"smartswap-backend","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connection.ts:551:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connection.ts:625:22)\n    at async continueScramConversation (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:187:13)\n    at async executeScram (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:114:3)\n    at async ScramSHA1.auth (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts:60:12)\n    at async performInitialHandshake (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connect.ts:164:7)\n    at async connect (D:\\Scheduler-Lovable\\backend\\node_modules\\mongodb\\src\\cmap\\connect.ts:43:5)","timestamp":"2025-05-28T08:25:09.295Z"}
{"level":"error","message":"Error inserting entry for safarjju:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.419Z"}
{"level":"error","message":"Error inserting entry for mmajedb:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.421Z"}
{"level":"error","message":"Error inserting entry for falahe:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.422Z"}
{"level":"error","message":"Error inserting entry for hijbarra:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.424Z"}
{"level":"error","message":"Error inserting entry for saifima:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.426Z"}
{"level":"error","message":"Error inserting entry for lumak:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.427Z"}
{"level":"error","message":"Error inserting entry for ayahh:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.429Z"}
{"level":"error","message":"Error inserting entry for qutobrq:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.431Z"}
{"level":"error","message":"Error inserting entry for aseabeda:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.433Z"}
{"level":"error","message":"Error inserting entry for ialkhati:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.436Z"}
{"level":"error","message":"Error inserting entry for ibramabd:","service":"smartswap-backend","timestamp":"2025-05-28T13:55:53.438Z"}
{"level":"error","message":"Error inserting entry for samaljub:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.260Z"}
{"level":"error","message":"Error inserting entry for aalharaw:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.261Z"}
{"level":"error","message":"Error inserting entry for aliabdha:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.262Z"}
{"level":"error","message":"Error inserting entry for muslehrm:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.263Z"}
{"level":"error","message":"Error inserting entry for jaradehe:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.264Z"}
{"level":"error","message":"Error inserting entry for lalhunit:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.265Z"}
{"level":"error","message":"Error inserting entry for shoqairr:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.266Z"}
{"level":"error","message":"Error inserting entry for saburahm:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.268Z"}
{"level":"error","message":"Error inserting entry for pfasaqer:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.269Z"}
{"level":"error","message":"Error inserting entry for abetamar:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.270Z"}
{"level":"error","message":"Error inserting entry for zakarana:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.271Z"}
{"level":"error","message":"Error inserting entry for omaalbis:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.272Z"}
{"level":"error","message":"Error inserting entry for alemaish:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.273Z"}
{"level":"error","message":"Error inserting entry for haitsule:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.275Z"}
{"level":"error","message":"Error inserting entry for cmaishas:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.276Z"}
{"level":"error","message":"Error inserting entry for abuspsuh:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.278Z"}
{"level":"error","message":"Error inserting entry for faahmadu:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.279Z"}
{"level":"error","message":"Error inserting entry for hasansas:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.280Z"}
{"level":"error","message":"Error inserting entry for sumaiaal:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.281Z"}
{"level":"error","message":"Error inserting entry for hamdanra:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.282Z"}
{"level":"error","message":"Error inserting entry for ejoudiab:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.283Z"}
{"level":"error","message":"Error inserting entry for nwerq:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.285Z"}
{"level":"error","message":"Error inserting entry for alzeasee:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.286Z"}
{"level":"error","message":"Error inserting entry for raalbada:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.287Z"}
{"level":"error","message":"Error inserting entry for dalbadai:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.288Z"}
{"level":"error","message":"Error inserting entry for khaljaza:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.289Z"}
{"level":"error","message":"Error inserting entry for raalquda:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.290Z"}
{"level":"error","message":"Error inserting entry for almafaye:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.291Z"}
{"level":"error","message":"Error inserting entry for takdalk:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.292Z"}
{"level":"error","message":"Error inserting entry for audaihar:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.293Z"}
{"level":"error","message":"Error inserting entry for alomarir:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.296Z"}
{"level":"error","message":"Error inserting entry for riyadpab:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.298Z"}
{"level":"error","message":"Error inserting entry for wahfatek:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.299Z"}
{"level":"error","message":"Error inserting entry for lumakhal:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.300Z"}
{"level":"error","message":"Error inserting entry for duhashah:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.301Z"}
{"level":"error","message":"Error inserting entry for mmanasee:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.303Z"}
{"level":"error","message":"Error inserting entry for jyazaabd:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.304Z"}
{"level":"error","message":"Error inserting entry for hantouli:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.306Z"}
{"level":"error","message":"Error inserting entry for mohalzab:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.309Z"}
{"level":"error","message":"Error inserting entry for malkawim:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.322Z"}
{"level":"error","message":"Error inserting entry for noabdale:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.340Z"}
{"level":"error","message":"Error inserting entry for aissmael:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.341Z"}
{"level":"error","message":"Error inserting entry for mariakta:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.343Z"}
{"level":"error","message":"Error inserting entry for rhamdako:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.344Z"}
{"level":"error","message":"Error inserting entry for isswsara:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.346Z"}
{"level":"error","message":"Error inserting entry for hasaabui:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.347Z"}
{"level":"error","message":"Error inserting entry for raysalah:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.349Z"}
{"level":"error","message":"Error inserting entry for seretais:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.351Z"}
{"level":"error","message":"Error inserting entry for vhuasmaa:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.352Z"}
{"level":"error","message":"Error inserting entry for ramrghan:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.353Z"}
{"level":"error","message":"Error inserting entry for szabalaw:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.355Z"}
{"level":"error","message":"Error inserting entry for qrasmieh:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.356Z"}
{"level":"error","message":"Error inserting entry for elayansh:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.358Z"}
{"level":"error","message":"Error inserting entry for alazzree:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.359Z"}
{"level":"error","message":"Error inserting entry for khalabue:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.361Z"}
{"level":"error","message":"Error inserting entry for aseealf:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.374Z"}
{"level":"error","message":"Error inserting entry for abuzaghl:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.376Z"}
{"level":"error","message":"Error inserting entry for aburakka:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.377Z"}
{"level":"error","message":"Error inserting entry for silihalj:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.378Z"}
{"level":"error","message":"Error inserting entry for dsziad:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.380Z"}
{"level":"error","message":"Error inserting entry for alshaerf:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.381Z"}
{"level":"error","message":"Error inserting entry for alzahrqa:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.383Z"}
{"level":"error","message":"Error inserting entry for liyismai:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.384Z"}
{"level":"error","message":"Error inserting entry for sababdal:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.386Z"}
{"level":"error","message":"Error inserting entry for omarpabu:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.389Z"}
{"level":"error","message":"Error inserting entry for zerien:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.393Z"}
{"level":"error","message":"Error inserting entry for abashour:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.397Z"}
{"level":"error","message":"Error inserting entry for hadeeusa:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.399Z"}
{"level":"error","message":"Error inserting entry for aisalame:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.401Z"}
{"level":"error","message":"Error inserting entry for eahmadya:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.402Z"}
{"level":"error","message":"Error inserting entry for htous:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.404Z"}
{"level":"error","message":"Error inserting entry for ramqalna:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.405Z"}
{"level":"error","message":"Error inserting entry for ereqatfi:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.406Z"}
{"level":"error","message":"Error inserting entry for issahuma:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.407Z"}
{"level":"error","message":"Error inserting entry for halqayam:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.408Z"}
{"level":"error","message":"Error inserting entry for layalnaw:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.409Z"}
{"level":"error","message":"Error inserting entry for gazadnad:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.410Z"}
{"level":"error","message":"Error inserting entry for ysaabdal:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.603Z"}
{"level":"error","message":"Error inserting entry for yazanofa:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.604Z"}
{"level":"error","message":"Error inserting entry for armomara:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.605Z"}
{"level":"error","message":"Error inserting entry for alobes:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.606Z"}
{"level":"error","message":"Error inserting entry for esraaals:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.607Z"}
{"level":"error","message":"Error inserting entry for hadeeals:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.608Z"}
{"level":"error","message":"Error inserting entry for kaladwan:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.609Z"}
{"level":"error","message":"Error inserting entry for nedalafa:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.611Z"}
{"level":"error","message":"Error inserting entry for gabukoma:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.612Z"}
{"level":"error","message":"Error inserting entry for razanhab:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.613Z"}
{"level":"error","message":"Error inserting entry for timjamal:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.614Z"}
{"level":"error","message":"Error inserting entry for suhghait:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.616Z"}
{"level":"error","message":"Error inserting entry for mznissa:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.618Z"}
{"level":"error","message":"Error inserting entry for ofakhrih:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.619Z"}
{"level":"error","message":"Error inserting entry for laalia:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.620Z"}
{"level":"error","message":"Error inserting entry for haafalky:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.621Z"}
{"level":"error","message":"Error inserting entry for rafahs:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.622Z"}
{"level":"error","message":"Error inserting entry for rtayseer:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.623Z"}
{"level":"error","message":"Error inserting entry for aseelalj:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.624Z"}
{"level":"error","message":"Error inserting entry for ssbeibar:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.625Z"}
{"level":"error","message":"Error inserting entry for subohm:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.626Z"}
{"level":"error","message":"Error inserting entry for alsaleht:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.627Z"}
{"level":"error","message":"Error inserting entry for albatasz:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.628Z"}
{"level":"error","message":"Error inserting entry for safarjju:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.629Z"}
{"level":"error","message":"Error inserting entry for issmahmo:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.629Z"}
{"level":"error","message":"Error inserting entry for ialzoub:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.630Z"}
{"level":"error","message":"Error inserting entry for hirzs:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.631Z"}
{"level":"error","message":"Error inserting entry for salahedg:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.633Z"}
{"level":"error","message":"Error inserting entry for ylalamoh:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.635Z"}
{"level":"error","message":"Error inserting entry for Shawesh:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.637Z"}
{"level":"error","message":"Error inserting entry for vsuleiah:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.638Z"}
{"level":"error","message":"Error inserting entry for bdorahma:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.639Z"}
{"level":"error","message":"Error inserting entry for raalmaja:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.641Z"}
{"level":"error","message":"Error inserting entry for malakale:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.642Z"}
{"level":"error","message":"Error inserting entry for vssereen:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.643Z"}
{"level":"error","message":"Error inserting entry for zainehab:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.644Z"}
{"level":"error","message":"Error inserting entry for alnusaiu:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.645Z"}
{"level":"error","message":"Error inserting entry for ibramabd:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.646Z"}
{"level":"error","message":"Error inserting entry for osaboust:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.647Z"}
{"level":"error","message":"Error inserting entry for mttotah:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.649Z"}
{"level":"error","message":"Error inserting entry for nidalals:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.652Z"}
{"level":"error","message":"Error inserting entry for alzriqat:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.654Z"}
{"level":"error","message":"Error inserting entry for emkhalif:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.655Z"}
{"level":"error","message":"Error inserting entry for emadhamm:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.656Z"}
{"level":"error","message":"Error inserting entry for khamimut:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.657Z"}
{"level":"error","message":"Error inserting entry for yousenal:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.657Z"}
{"level":"error","message":"Error inserting entry for baabukhu:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.658Z"}
{"level":"error","message":"Error inserting entry for mahmoamu:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.659Z"}
{"level":"error","message":"Error inserting entry for iseeg:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.660Z"}
{"level":"error","message":"Error inserting entry for qaallamf:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.661Z"}
{"level":"error","message":"Error inserting entry for ahabusar:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.663Z"}
{"level":"error","message":"Error inserting entry for abusumam:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.664Z"}
{"level":"error","message":"Error inserting entry for saifima:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.666Z"}
{"level":"error","message":"Error inserting entry for mashhora:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.667Z"}
{"level":"error","message":"Error inserting entry for saleibrj:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.669Z"}
{"level":"error","message":"Error inserting entry for alwahedy:","service":"smartswap-backend","timestamp":"2025-05-28T13:57:31.671Z"}
{"level":"error","message":"Error inserting entry for rizikm: RealScheduleEntry validation failed: lunch: Lunch time is required, break1: Break1 time is required, break2: Break2 time is required","service":"smartswap-backend","timestamp":"2025-05-28T13:59:31.805Z"}
{"level":"error","message":"Error inserting entry for hmokhali: RealScheduleEntry validation failed: lunch: Lunch time is required, break1: Break1 time is required, break2: Break2 time is required","service":"smartswap-backend","timestamp":"2025-05-28T13:59:31.808Z"}
{"level":"error","message":"Error inserting entry for alhadedr: RealScheduleEntry validation failed: lunch: Lunch time is required, break1: Break1 time is required, break2: Break2 time is required","service":"smartswap-backend","timestamp":"2025-05-28T13:59:31.812Z"}
{"level":"error","message":"Real schedule stats error: Cannot read properties of undefined (reading 'db')","service":"smartswap-backend","stack":"TypeError: Cannot read properties of undefined (reading 'db')\n    at D:\\Scheduler-Lovable\\backend\\src\\app.ts:58:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-05-28T14:27:15.920Z"}
{"level":"error","message":"Real schedule stats error: Cannot read properties of undefined (reading 'readyState')","service":"smartswap-backend","stack":"TypeError: Cannot read properties of undefined (reading 'readyState')\n    at D:\\Scheduler-Lovable\\backend\\src\\app.ts:56:29\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-05-28T14:27:57.010Z"}
{"level":"error","message":"Real schedule stats error: Cannot read properties of undefined (reading 'readyState')","service":"smartswap-backend","stack":"TypeError: Cannot read properties of undefined (reading 'readyState')\n    at D:\\Scheduler-Lovable\\backend\\src\\app.ts:57:29\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-05-28T16:53:07.791Z"}
{"code":"MODULE_NOT_FOUND","expose":true,"level":"error","message":"Error occurred: Cannot find module '../encodings'\nRequire stack:\n- D:\\Scheduler-Lovable\\node_modules\\iconv-lite\\lib\\index.js\n- D:\\Scheduler-Lovable\\node_modules\\raw-body\\index.js\n- D:\\Scheduler-Lovable\\node_modules\\body-parser\\lib\\read.js\n- D:\\Scheduler-Lovable\\node_modules\\body-parser\\lib\\types\\json.js\n- D:\\Scheduler-Lovable\\node_modules\\body-parser\\index.js\n- D:\\Scheduler-Lovable\\node_modules\\express\\lib\\express.js\n- D:\\Scheduler-Lovable\\node_modules\\express\\index.js\n- D:\\Scheduler-Lovable\\backend\\src\\app.ts\n- D:\\Scheduler-Lovable\\backend\\src\\index.ts","requireStack":["D:\\Scheduler-Lovable\\node_modules\\iconv-lite\\lib\\index.js","D:\\Scheduler-Lovable\\node_modules\\raw-body\\index.js","D:\\Scheduler-Lovable\\node_modules\\body-parser\\lib\\read.js","D:\\Scheduler-Lovable\\node_modules\\body-parser\\lib\\types\\json.js","D:\\Scheduler-Lovable\\node_modules\\body-parser\\index.js","D:\\Scheduler-Lovable\\node_modules\\express\\lib\\express.js","D:\\Scheduler-Lovable\\node_modules\\express\\index.js","D:\\Scheduler-Lovable\\backend\\src\\app.ts","D:\\Scheduler-Lovable\\backend\\src\\index.ts"],"service":"smartswap-backend","stack":"Error: Cannot find module '../encodings'\nRequire stack:\n- D:\\Scheduler-Lovable\\node_modules\\iconv-lite\\lib\\index.js\n- D:\\Scheduler-Lovable\\node_modules\\raw-body\\index.js\n- D:\\Scheduler-Lovable\\node_modules\\body-parser\\lib\\read.js\n- D:\\Scheduler-Lovable\\node_modules\\body-parser\\lib\\types\\json.js\n- D:\\Scheduler-Lovable\\node_modules\\body-parser\\index.js\n- D:\\Scheduler-Lovable\\node_modules\\express\\lib\\express.js\n- D:\\Scheduler-Lovable\\node_modules\\express\\index.js\n- D:\\Scheduler-Lovable\\backend\\src\\app.ts\n- D:\\Scheduler-Lovable\\backend\\src\\index.ts\n    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (D:\\Scheduler-Lovable\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.getCodec (D:\\Scheduler-Lovable\\node_modules\\iconv-lite\\lib\\index.js:63:27)","status":400,"statusCode":400,"timestamp":"2025-05-28T18:25:37.675Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for bdorahma: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:43.735Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ylalamoh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:43.923Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abushanh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:44.117Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for yahaddaz: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:44.307Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abdamohp: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:44.495Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for harounha: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:44.680Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for haalsala: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:44.865Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for baabukhu: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:45.052Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mariakta: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:45.240Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for noabdale: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:45.428Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for htous: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:45.613Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for issahuma: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:45.815Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for shawesh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:46.011Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mohalzab: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:46.214Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for raysalah: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:46.415Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for laalia: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:46.602Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for kaladwan: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:46.817Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for takdalk: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:47.002Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for eahmadya: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:47.187Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aissmael: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:47.388Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for dhmohann: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:47.584Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aseelalj: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:47.773Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for yousenal: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:47.964Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for cmaishas: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:48.151Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for nedalafa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:48.340Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for rhamdako: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:48.537Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aburakka: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:48.729Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for muslehrm: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:48.929Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for silihalj: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:49.118Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for haafalky: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:49.316Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alobes: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:49.511Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abetamar: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:49.709Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hirzs: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:49.899Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ysaabdal: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:50.094Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ramrghan: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:50.293Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for lalhunit: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:50.493Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for zaghabsa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:50.691Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for elayansh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:50.898Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for raalbada: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:51.112Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for qrasmieh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:51.307Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for saleibrj: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:51.508Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alzeasee: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:51.712Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for jaradehe: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:51.906Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alwahedy: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:52.106Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for faahmadu: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:52.307Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for khalabue: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:52.506Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abushaeb: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:52.714Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for khamimut: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:52.905Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abuspsuh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:53.103Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for elzoghei: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:53.302Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abdabuli: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:53.503Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for lmotothm: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:53.704Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for saqqusai: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:53.906Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for snouomar: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:54.099Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for dalbadai: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:54.297Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mabuguos: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:54.497Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for rasaifed: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:54.699Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for iabdelqa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:54.902Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for emadhamm: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:55.104Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ibramabd: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:55.304Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for skhaledq: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:55.502Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alzriqat: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:55.694Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alderuha: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:55.891Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abuobnoo: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:56.093Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for omarabab: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:56.285Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for safarjju: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:56.474Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aisalame: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:56.671Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for caalrejj: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:56.862Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for Shawesh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:57.055Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alqudoma: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:57.245Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abashour: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:57.431Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for zerien: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:57.614Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for liyismai: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:57.799Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aseealf: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:57.985Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for saburahm: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:58.171Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alzahrqa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:58.367Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for emkhalif: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:58.563Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for armvhish: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:58.762Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for iseeg: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:58.954Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for kmalakja: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:59.154Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for samaljub: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:59.356Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alnusaiu: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:59.558Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for sababdal: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:59.760Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for uyasjoma: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:02:59.953Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hadeeusa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:00.141Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hantouli: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:00.338Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mznissa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:00.528Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for yazanofa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:00.718Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aalharaw: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:00.908Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for pfasaqer: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:01.106Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for subohm: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:01.296Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for rafahs: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:01.486Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for malkawim: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:01.678Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for issmahmo: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:01.877Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for fakhrihm: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:02.078Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ialzoub: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:02.267Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ramqalna: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:02.465Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for raalquda: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:02.654Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for almafaye: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:02.842Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for nwerq: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:03.027Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for maalasma: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:03.216Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alaqarbe: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:03.412Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ereqatfi: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:03.612Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aldeemac: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:03.814Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for malakale: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:04.004Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ssbeibar: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:04.200Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for anaaldrk: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:04.397Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for omaalbis: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:04.585Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mmanasee: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:04.785Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for vsuleiah: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:04.985Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for khaljaza: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:05.182Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for zmunmahm: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:05.380Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ejoudiab: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:05.571Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hazemmod: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:05.758Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for riyadpab: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:05.943Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for yosqanba: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:06.140Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ismbayan: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:06.327Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for vhuasmaa: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:06.516Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for suhghait: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:06.704Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for lumakhal: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:06.890Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abdewram: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:07.074Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for nidalals: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:07.259Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for timjamal: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:07.444Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for izeddina: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:07.629Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alhajjea: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:07.816Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mohdnidn: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:08.007Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hasaabui: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:08.201Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for duhashah: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:08.398Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aadnanat: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:08.598Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for salhmuta: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:08.786Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mashhora: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:08.982Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for dsziad: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:09.167Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for armomara: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:09.353Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abuzaghl: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:09.538Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hadeeals: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:09.733Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alazzree: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:09.920Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alshaerf: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:10.114Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mttotah: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:10.298Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for esraaals: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:10.488Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for shoqairr: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:10.674Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for albatasz: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:10.865Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for osaboust: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:11.064Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alsashra: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:11.261Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hasansas: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:11.458Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for sumaiaal: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:11.653Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for omarpabu: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:11.841Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mmajedb: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:12.034Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for falahe: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:12.224Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for akkgdeem: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:12.414Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hijbarra: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:12.602Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for touqanf: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:12.786Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for saifima: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:12.977Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for lumak: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:13.163Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ayahh: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:13.351Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for qutobrq: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:13.540Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aseabeda: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:13.725Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ialkhati: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:13.916Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alomarir: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:14.114Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for halqayam: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:14.304Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for layalnaw: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:14.501Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for iskhater: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:14.690Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for szabalaw: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:14.879Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hamdanra: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:15.067Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alsaleht: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:15.253Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for rtayseer: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:15.474Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for raalmaja: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:15.669Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for salahedg: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:15.861Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for wahfatek: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:16.046Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alemaish: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:16.232Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for adannya: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:16.416Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for gazadnad: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:16.602Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for zakarana: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:16.786Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for audaihar: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:16.973Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for aliabdha: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:17.158Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ofakhrih: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:17.344Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for jyazaabd: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:17.540Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for qaallamf: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:17.731Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for ahabusar: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:17.927Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for abusumam: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:18.116Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for haitsule: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:18.302Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for vssereen: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:18.486Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for zainehab: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:18.672Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for gabukoma: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:18.867Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for seretais: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:19.069Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for mahmoamu: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:19.266Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for razanhab: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:19.455Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for isswsara: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:19.642Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for hzzamel: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:19.836Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for madyaser: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:20.033Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for moaubtou: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:20.222Z"}
{"_message":"User validation failed","errors":{"email":{"kind":"regexp","message":"Please enter a valid email","name":"ValidatorError","path":"email","properties":{"message":"Please enter a valid email","path":"email","regexp":{},"type":"regexp","value":"<EMAIL>"},"value":"<EMAIL>"}},"level":"error","message":"Error creating user for alamadma: User validation failed: email: Please enter a valid email","service":"smartswap-backend","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at model.Document.invalidate (D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3343:32)\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\document.js:3104:17\n    at D:\\Scheduler-Lovable\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-05-28T19:03:20.408Z"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"bdorahma\" }","index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"bdorahma"}},"index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"bdorahma"},"level":"error","message":"Error <NAME_EMAIL>: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"bdorahma\" }","service":"smartswap-backend","stack":"MongoServerError: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"bdorahma\" }\n    at InsertOneOperation.execute (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\insert.ts:88:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:283:14)\n    at async executeOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:115:12)\n    at async Collection.insertOne (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\collection.ts:285:12)","timestamp":"2025-05-28T19:16:40.546Z"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"ylalamoh\" }","index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"ylalamoh"}},"index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"ylalamoh"},"level":"error","message":"Error <NAME_EMAIL>: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"ylalamoh\" }","service":"smartswap-backend","stack":"MongoServerError: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"ylalamoh\" }\n    at InsertOneOperation.execute (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\insert.ts:88:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:283:14)\n    at async executeOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:115:12)\n    at async Collection.insertOne (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\collection.ts:285:12)","timestamp":"2025-05-28T19:16:41.032Z"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"abushanh\" }","index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"abushanh"}},"index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"abushanh"},"level":"error","message":"Error <NAME_EMAIL>: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"abushanh\" }","service":"smartswap-backend","stack":"MongoServerError: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"abushanh\" }\n    at InsertOneOperation.execute (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\insert.ts:88:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:283:14)\n    at async executeOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:115:12)\n    at async Collection.insertOne (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\collection.ts:285:12)","timestamp":"2025-05-28T19:16:41.508Z"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"yahaddaz\" }","index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"yahaddaz"}},"index":0,"keyPattern":{"userLogin":1},"keyValue":{"userLogin":"yahaddaz"},"level":"error","message":"Error <NAME_EMAIL>: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"yahaddaz\" }","service":"smartswap-backend","stack":"MongoServerError: E11000 duplicate key error collection: smartswap.users index: userLogin_1 dup key: { userLogin: \"yahaddaz\" }\n    at InsertOneOperation.execute (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\insert.ts:88:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:283:14)\n    at async executeOperation (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\operations\\execute_operation.ts:115:12)\n    at async Collection.insertOne (D:\\Scheduler-Lovable\\node_modules\\mongodb\\src\\collection.ts:285:12)","timestamp":"2025-05-28T19:16:42.019Z"}
